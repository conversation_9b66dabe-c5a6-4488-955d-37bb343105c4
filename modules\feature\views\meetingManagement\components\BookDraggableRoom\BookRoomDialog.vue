<template>
    <div>
        <el-dialog
            :visible.sync="dialogVisible"
            width="600px"
            :title="id ? '修改会议室' : '预定会议室'"
            @close="closeDialog"
        >
            <el-form
                ref="bookingForm"
                :model="formData"
                :rules="formRules"
                label-width="100px"
                label-position="left"
            >
                <!-- 预定日期 -->
                <el-form-item label="预定日期" prop="date">
                    <el-date-picker
                        v-model="formData.date"
                        type="date"
                        placeholder="选择日期"
                        format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd"
                        style="width: 100%"
                        :picker-options="datePickerOptions"
                    />
                </el-form-item>

                <!-- 预定时间 -->
                <el-form-item label="预定时间" prop="timeRange">
                    <el-time-picker
                        class="time-picker"
                        size="small"
                        is-range
                        v-model="formData.timeRange"
                        range-separator="至"
                        start-placeholder="开始时间"
                        end-placeholder="结束时间"
                        placeholder="选择时间范围"
                        format="HH:mm"
                        value-format="HH:mm"
                        clearable
                    >
                    </el-time-picker>
                </el-form-item>

                <!-- 会议室 -->
                <el-form-item label="会议室" prop="address">
                    <el-select
                        v-if="id"
                        v-model="formData.address"
                        placeholder="请选择会议室"
                        style="width: 100%"
                    >
                        <el-option
                            v-for="room in meetingRooms"
                            :key="room.value"
                            :label="room.label"
                            :value="room.value"
                        />
                    </el-select>
                    <el-select
                        v-else
                        v-model="formData.address"
                        placeholder="请选择会议室"
                        style="width: 100%"
                        multiple
                    >
                        <el-option
                            v-for="room in meetingRooms"
                            :key="room.value"
                            :label="room.label"
                            :value="room.value"
                        />
                    </el-select>
                </el-form-item>

                <!-- 会议名称 -->
                <el-form-item label="会议名称" prop="meetingName">
                    <el-input
                        v-model="formData.meetingName"
                        placeholder="请输入会议名称"
                    />
                </el-form-item>
            </el-form>

            <div slot="footer">
                <el-button @click="closeDialog">取 消</el-button>
                <el-button type="primary" @click="save">确 定</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
export default {
    name: 'BookRoomDialog',
    components: {},
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        // 待编辑的会议室id
        id: {
            type: [String, Number],
            default: ''
        },
        meetingInfo: {
            type: Object,
            default: () => ({})
        },
        meetingRooms: {
            type: Array,
            default: () => []
        },
        // 会议室冲突数据，从外部传入
        allMeetingInfo: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            conflictData: [],
            formData: {
                date: '',
                timeRange: '',
                address: [],
                meetingName: ''
            },
            formRules: {
                date: [
                    {
                        required: true,
                        message: '请选择预定日期',
                        trigger: 'change'
                    }
                ],
                timeRange: [
                    {
                        required: true,
                        message: '请选择时间',
                        trigger: 'change'
                    }
                ],
                address: [
                    {
                        required: true,
                        message: '请选择会议室',
                        trigger: 'change'
                    }
                ],
                meetingName: [
                    {
                        required: true,
                        message: '请输入会议名称',
                        trigger: 'blur'
                    }
                ]
            },
            datePickerOptions: {
                disabledDate(time) {
                    // 禁用今天之前的日期
                    return time.getTime() < Date.now() - 8.64e7;
                }
            },
            originalMeeting: {}
        };
    },
    computed: {
        dialogVisible: {
            get() {
                return this.visible;
            },
            set(value) {
                this.$emit('update:visible', value);
            }
        }
    },
    watch: {
        dialogVisible(newVal) {
            if (newVal) {
                this.initFormData();
            }
        }
    },
    methods: {
        /**
         * 初始化表单数据
         */
        initFormData() {
            this.formData = {
                date: this.meetingInfo.date || '',
                timeRange: this.meetingInfo.timeRange || '',
                address: this.meetingInfo.address || [],
                meetingName: this.meetingInfo.meetingName || ''
            };
            this.originalMeeting = this.$tools.cloneDeep(this.meetingInfo);
        },

        /**
         * 重置表单
         */
        resetForm() {
            if (this.$refs.bookingForm) {
                this.$refs.bookingForm.resetFields();
            }
        },

        /**
         * 关闭弹窗
         */
        closeDialog() {
            this.resetForm();
            this.dialogVisible = false;
        },

        /**
         * 检查会议室是否有冲突
         */
        async checkRoomConflict() {
            try {
                const [startTime, endTime] = this.formData.timeRange;
                const conflictsByRoom = {};
                const toBeConfirmedRoom = this.id
                    ? [this.formData.address]
                    : this.formData.address;

                // 检查每个选中的会议室是否有冲突
                toBeConfirmedRoom.forEach((address) => {
                    const roomConflicts = this.allMeetingInfo.filter(
                        (meeting) => {
                            return this.checkMeetingTimeConflict(
                                meeting,
                                address,
                                startTime,
                                endTime
                            );
                        }
                    );

                    if (roomConflicts.length > 0) {
                        const roomInfo = this.meetingRooms.find(
                            (room) => room.value === address
                        );
                        conflictsByRoom[address] = {
                            roomName: roomInfo
                                ? roomInfo.label
                                : `会议室${address}`,
                            conflicts: roomConflicts
                        };
                    }
                });

                const allConflicts = Object.values(conflictsByRoom);

                return {
                    hasConflict: allConflicts.length > 0,
                    conflicts: allConflicts
                };
            } catch (error) {
                console.error('检查会议室冲突失败:', error);
                throw error;
            }
        },

        /**
         * 检查单个会议是否与新时间冲突
         * @param {Object} meeting - 会议信息
         * @param {String} address - 会议室地址
         * @param {String} startTime - 新会议开始时间
         * @param {String} endTime - 新会议结束时间
         * @returns {Boolean} 是否冲突
         */
        checkMeetingTimeConflict(meeting, address, startTime, endTime) {
            console.log(meeting.address, 'meeting.address');
            console.log(address, 'address');

            // 只检查同一会议室的会议
            if (meeting.address !== address) {
                return false;
            }

            // 获取会议时间，兼容不同的字段名
            const meetingStartTime = meeting.startTime || meeting.beginTime;
            const meetingEndTime = meeting.endTime || meeting.endTime;

            if (!meetingStartTime || !meetingEndTime) {
                return false;
            }

            const meetingStart = this.timeToMinutes(meetingStartTime);
            const meetingEnd = this.timeToMinutes(meetingEndTime);
            const newStart = this.timeToMinutes(startTime);
            const newEnd = this.timeToMinutes(endTime);

            // 编辑模式下的特殊处理
            if (
                this.id &&
                this.originalMeeting &&
                this.originalMeeting.address === address
            ) {
                return this.checkEditModeConflict(
                    meeting,
                    meetingStart,
                    meetingEnd,
                    newStart,
                    newEnd
                );
            }

            // 常规的时间重叠检查
            return !(newEnd <= meetingStart || newStart >= meetingEnd);
        },

        /**
         * 编辑模式下的冲突检查
         * @param {Object} meeting - 会议信息
         * @param {Number} meetingStart - 会议开始时间（分钟）
         * @param {Number} meetingEnd - 会议结束时间（分钟）
         * @param {Number} newStart - 新时间开始（分钟）
         * @param {Number} newEnd - 新时间结束（分钟）
         * @returns {Boolean} 是否冲突
         */
        checkEditModeConflict(
            meeting,
            meetingStart,
            meetingEnd,
            newStart,
            newEnd
        ) {
            const originalStart = this.timeToMinutes(
                this.originalMeeting.timeRange[0]
            );
            const originalEnd = this.timeToMinutes(
                this.originalMeeting.timeRange[1]
            );

            // 如果是当前正在编辑的会议，跳过冲突检查
            if (meeting.id === this.id) {
                return false;
            }

            // 检查会议时间与原时间是否重叠
            const meetingOverlapWithOriginal = !(
                meetingEnd <= originalStart || meetingStart >= originalEnd
            );
            const newTimeOverlapWithOriginal = !(
                newEnd <= originalStart || newStart >= originalEnd
            );

            if (meetingOverlapWithOriginal && newTimeOverlapWithOriginal) {
                // 计算新时间中不与原时间重叠的部分
                const newTimeSegments = this.calculateNonOverlappingSegments(
                    newStart,
                    newEnd,
                    originalStart,
                    originalEnd
                );

                // 检查这些不重叠的部分是否与其他会议冲突
                return newTimeSegments.some(
                    (segment) =>
                        !(
                            segment[1] <= meetingStart ||
                            segment[0] >= meetingEnd
                        )
                );
            }

            // 常规的时间重叠检查
            return !(newEnd <= meetingStart || newStart >= meetingEnd);
        },

        /**
         * 计算新时间中不与原时间重叠的部分
         * @param {Number} newStart - 新时间开始
         * @param {Number} newEnd - 新时间结束
         * @param {Number} originalStart - 原时间开始
         * @param {Number} originalEnd - 原时间结束
         * @returns {Array} 不重叠的时间段数组
         */
        calculateNonOverlappingSegments(
            newStart,
            newEnd,
            originalStart,
            originalEnd
        ) {
            const segments = [];

            // 新时间在原时间之前的部分
            if (newStart < originalStart) {
                segments.push([newStart, Math.min(newEnd, originalStart)]);
            }

            // 新时间在原时间之后的部分
            if (newEnd > originalEnd) {
                segments.push([Math.max(newStart, originalEnd), newEnd]);
            }

            return segments;
        },

        /**
         * 将时间字符串转换为分钟数
         * @param {string} timeStr - 时间字符串，格式为 HH:mm
         * @returns {number} 转换后的分钟数
         */
        timeToMinutes(timeStr) {
            const [hours, minutes] = timeStr.split(':').map(Number);
            return hours * 60 + minutes;
        },

        /**
         * 保存会议预订
         */
        async save() {
            try {
                // 表单验证
                const valid = await this.$refs.bookingForm.validate();
                if (!valid) return;

                // 检查会议室冲突
                const conflictResult = await this.checkRoomConflict();

                if (conflictResult.hasConflict) {
                    this.$alert(
                        `${conflictResult.conflicts
                            .map((i) => `[${i.roomName}] 冲突`)
                            .join('<br/>')}`,
                        '会议室冲突不能提交',
                        {
                            dangerouslyUseHTMLString: true
                        }
                    );
                    return;
                }
                const { address, date, timeRange, meetingName } = this.formData;
                // 调用保存接口
                const params = {
                    // 编辑的时候只能选择单个地址，新增的时候可以多个
                    address: this.id ? address : address.join(','),
                    beginDate: date,
                    endDate: date,
                    beginTime: timeRange[0],
                    endTime: timeRange[1],
                    meetingName,
                    oriMeetingId: this.id || undefined,
                    remindType: '',
                    repeatType: '',
                    isInterval: '',
                    desc: ''
                };
                const api = this.id
                    ? this.$service.feature.meetingRoom.editRoom
                    : this.$service.feature.meetingRoom.bookRoom;
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }

                this.$message.success('保存成功');
                this.$emit('success', params);
                this.closeDialog();
            } catch (error) {
                console.error('保存失败:', error);
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.time-range-container {
    display: flex;
    align-items: center;
    width: 100%;

    .time-separator {
        margin: 0 10px;
        color: #606266;
        font-weight: bold;
    }
}

::v-deep .el-form-item__label {
    font-weight: 500;
    color: #303133;
}

::v-deep .el-date-editor,
::v-deep .el-select {
    width: 100%;
}

::v-deep .el-input__inner {
    border-radius: 4px;
}

::v-deep .el-dialog__body {
    padding: 20px 20px 10px 20px;
}

::v-deep .el-dialog__footer {
    padding: 10px 20px 20px 20px;
    text-align: right;
}
</style>
