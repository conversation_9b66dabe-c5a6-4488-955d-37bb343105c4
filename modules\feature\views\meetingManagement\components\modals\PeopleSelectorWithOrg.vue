<template>
    <el-dialog
        title="选择人员"
        :visible.sync="dialogVisible"
        width="1000px"
        :close-on-click-modal="false"
        @close="handleClose"
    >
        <div class="people-tree-container">
            <!-- 全部人员根节点 -->
            <div class="tree-node root-node">
                <div class="node-content">
                    <el-checkbox
                        :indeterminate="isIndeterminate"
                        :value="checkAll"
                        @change="handleCheckAllChange"
                    >
                        全部人员
                    </el-checkbox>
                </div>

                <!-- 部门列表 -->
                <div class="children-container">
                    <div
                        v-for="org in orgList"
                        :key="org.id"
                        class="tree-node org-node"
                    >
                        <div class="node-content">
                            <el-checkbox
                                :indeterminate="getOrgIndeterminate(org)"
                                :value="isOrgSelected(org)"
                                @change="toggleOrgSelection(org)"
                            >
                                {{ org.label }}
                            </el-checkbox>
                        </div>

                        <!-- 人员网格 -->
                        <div class="people-grid-container">
                            <div class="people-grid">
                                <div
                                    v-for="person in getOrgPeople(org.id)"
                                    :key="person.id"
                                    class="person-item"
                                    @click="togglePersonSelection(person)"
                                >
                                    <el-checkbox
                                        :value="isPersonSelected(person)"
                                        @change="togglePersonSelection(person)"
                                        class="person-checkbox"
                                    />
                                    <span class="person-name">{{
                                        person.name
                                    }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div slot="footer" class="dialog-footer">
            <el-button @click="handleClose">取 消</el-button>
            <el-button type="primary" @click="handleConfirm">
                确 定 ({{ selectedPeople.length }})
            </el-button>
        </div>
    </el-dialog>
</template>

<script>
export default {
    name: 'PeopleSelectorWithOrg',
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        // 已选择的人员列表
        value: {
            type: Array,
            default: () => []
        },
        // 是否多选
        multiple: {
            type: Boolean,
            default: true
        }
    },
    data() {
        return {
            orgList: [],
            allPeopleList: [],
            selectedPeople: [],
            checkAll: false,
            isIndeterminate: false
        };
    },
    computed: {
        dialogVisible: {
            get() {
                return this.visible;
            },
            set(val) {
                this.$emit('update:visible', val);
            }
        }
    },
    watch: {
        visible(newVal) {
            if (newVal) {
                this.initData();
            }
        },
        value: {
            immediate: true,
            handler(newVal) {
                this.selectedPeople = [...(newVal || [])];
            }
        },
        selectedPeople() {
            this.updateCheckAllStatus();
        }
    },
    methods: {
        async initData() {
            await this.getOrgData();
            await this.getAllPeopleList();
        },

        async getOrgData() {
            try {
                this.orgList = [
                    { id: '2', label: '结构开发部' },
                    { id: '3', label: '硬件开发部' },
                    { id: '4', label: '软件开发部' }
                ];
            } catch (error) {
                console.error('获取组织架构失败:', error);
                this.$message.error('获取组织架构失败');
            }
        },

        async getAllPeopleList() {
            try {
                const names = [
                    '张三丰',
                    '张三',
                    '张二丰',
                    '张三十',
                    '张二十',
                    '张三',
                    '张二丰',
                    '张三丰',
                    '张三十',
                    '张二十'
                ];
                const allPeople = [];

                this.orgList.forEach((org) => {
                    // 每个部门生成40个人员，按10列4行排列
                    for (let i = 0; i < 40; i++) {
                        allPeople.push({
                            id: `${org.id}_${i + 1}`,
                            name: names[i % names.length],
                            orgId: org.id
                        });
                    }
                });

                this.allPeopleList = allPeople;
            } catch (error) {
                console.error('获取人员列表失败:', error);
                this.$message.error('获取人员列表失败');
            }
        },

        updateCheckAllStatus() {
            const checkedCount = this.selectedPeople.length;
            this.checkAll = checkedCount === this.allPeopleList.length;
            this.isIndeterminate =
                checkedCount > 0 && checkedCount < this.allPeopleList.length;
        },

        handleCheckAllChange(val) {
            if (val) {
                this.selectedPeople = [...this.allPeopleList];
            } else {
                this.selectedPeople = [];
            }
        },

        isPersonSelected(person) {
            return this.selectedPeople.some((p) => p.id === person.id);
        },

        togglePersonSelection(person) {
            if (this.isPersonSelected(person)) {
                this.removeSelectedPerson(person);
            } else if (!this.multiple) {
                this.selectedPeople = [person];
            } else {
                this.selectedPeople.push(person);
            }
        },

        removeSelectedPerson(person) {
            const index = this.selectedPeople.findIndex(
                (p) => p.id === person.id
            );
            if (index > -1) {
                this.selectedPeople.splice(index, 1);
            }
        },

        clearAllSelected() {
            this.selectedPeople = [];
        },

        getOrgPeople(orgId) {
            return this.allPeopleList.filter(
                (person) => person.orgId === orgId
            );
        },

        isOrgSelected(org) {
            const orgPeople = this.getOrgPeople(org.id);
            return (
                orgPeople.length > 0 &&
                orgPeople.every((person) => this.isPersonSelected(person))
            );
        },

        getOrgIndeterminate(org) {
            const orgPeople = this.getOrgPeople(org.id);
            const selectedCount = orgPeople.filter((person) =>
                this.isPersonSelected(person)
            ).length;
            return selectedCount > 0 && selectedCount < orgPeople.length;
        },

        toggleOrgSelection(org) {
            const orgPeople = this.getOrgPeople(org.id);
            const isSelected = this.isOrgSelected(org);

            if (isSelected) {
                // 取消选择该组织下的所有人员
                orgPeople.forEach((person) => {
                    this.removeSelectedPerson(person);
                });
            } else {
                // 选择该组织下的所有人员
                orgPeople.forEach((person) => {
                    if (!this.isPersonSelected(person)) {
                        this.selectedPeople.push(person);
                    }
                });
            }
        },

        handleClose() {
            this.dialogVisible = false;
        },

        handleConfirm() {
            this.$emit('input', this.selectedPeople);
            this.$emit('confirm', this.selectedPeople);
            this.handleClose();
        }
    }
};
</script>

<style lang="scss" scoped>
.people-tree-container {
    max-height: 500px;
    overflow-y: auto;
}

.tree-node {
    .node-content {
        display: flex;
        align-items: center;
        padding: 8px 0;
    }

    &.org-node {
        .node-content {
            padding-left: 20px;
        }
    }
}

.people-grid-container {
    padding: 8px 0 8px 40px;

    .people-grid {
        display: grid;
        grid-template-columns: repeat(10, 1fr);
        gap: 6px;

        .person-item {
            display: flex;
            align-items: center;
            padding: 4px 8px;
            cursor: pointer;
            height: 32px;
            font-size: 12px;

            .person-checkbox {
                margin-right: 6px;
            }

            .person-name {
                flex: 1;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        }
    }
}

.dialog-footer {
    text-align: right;
    padding-top: 16px;
}
</style>
